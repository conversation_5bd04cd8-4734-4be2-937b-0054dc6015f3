import React, { useState, useRef, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import MakonisLogo from "../Asserts/Makonis-Logo.png";

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [aboutDropdownOpen, setAboutDropdownOpen] = useState(false);
  const [businessDropdownOpen, setBusinessDropdownOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();
  const aboutDropdownRef = useRef(null);
  const businessDropdownRef = useRef(null);
  const headerRef = useRef(null);
  const logoRef = useRef(null);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
    if (aboutDropdownOpen) setAboutDropdownOpen(false);
    if (businessDropdownOpen) setBusinessDropdownOpen(false);
  };

  useEffect(() => {
    if (headerRef.current) {
      headerRef.current.style.opacity = '0';
      headerRef.current.style.transform = 'translateY(-20px)';
      setTimeout(() => {
        headerRef.current.style.transition = 'all 0.8s ease';
        headerRef.current.style.opacity = '1';
        headerRef.current.style.transform = 'translateY(0)';
      }, 100);
    }
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const openTransition = 'opacity 0.3s ease, transform 0.3s ease, visibility 0s linear 0s';
  const closeTransition = 'opacity 0.3s ease, transform 0.3s ease, visibility 0s linear 0.3s';

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (aboutDropdownRef.current && !aboutDropdownRef.current.contains(event.target) && !event.target.closest('.who-we-are-dropdown-toggle')) {
        setAboutDropdownOpen(false);
      }
      if (businessDropdownRef.current && !businessDropdownRef.current.contains(event.target) && !event.target.closest('.business-dropdown-toggle')) {
        setBusinessDropdownOpen(false);
      }
      
      const openNestedSubmenus = document.querySelectorAll('.dropdown-menu .dropdown-submenu > .dropdown-menu[style*="visibility: visible"]');
      openNestedSubmenus.forEach((submenu) => {
        // Check if the click is outside the submenu and not on any toggle that might control it
        if (!submenu.contains(event.target) && !event.target.closest('.dropdown-submenu > a')) {
          submenu.style.transition = closeTransition;
          submenu.style.opacity = '0';
          submenu.style.visibility = 'hidden';
          submenu.style.transform = 'translateX(-10px)'; // Or its initial closed transform
        }
      });
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [closeTransition]); // Add closeTransition to dependency array if it's defined outside useEffect and can change

  const navItems = [
    { name: "Home", path: "/" },
    {
      name: "Who we are",
      path: "/#about",
      isDropdown: true,
      dropdownItems: [
        { name: "About us", path: "/about-us" },
        { name: "Our leadership", path: "/team" },
        { name: "Our Business model", path: "/business-model" },
        { name: "Life At Makonis", path: "/life-at-makonis" },
      ],
    },
    {
      name: "Business",
      path: "/#business",
      isDropdown: true,
      dropdownItems: [
        {
          name: "IT Services",
          path: "/it-services",
          icon: "fa-laptop",
          isDropdown: true,
          dropdownItems: [
            { name: "Staff augmentation (India & US)", path: "/staff-augmentation" },
            { name: "Integration", path: "/integration" },
            { name: "Testing", path: "/testing" },
            { name: "Web development", path: "/web-development" },
          ]
        },
        {
          name: "Products",
          path: "/products",
          icon: "fa-box",
          isDropdown: true,
          dropdownItems: [
            { name: "IoT", path: "/iot" },
            { name: "AI", path: "/ai" },
          ]
        },
        {
          name: "Semiconductors",
          path: "/semiconductors",
          icon: "fa-microchip",
          isDropdown: true,
          dropdownItems: [
            { name: "Physical Design", path: "/physical-design" },
            { name: "Physical verification", path: "/physical-verification" },
          ]
        },
      ],
    },
    { name: "Case Studies", path: "/case-studies" },
    { name: "Client testimonials", path: "/testimonials" },
    { name: "Careers", path: "/careers" },
    { name: "Contact Us", path: "/contact" },
  ];

  const headerStyle = {
    background: scrolled ? 'rgba(255, 255, 255, 0.98)' : 'rgba(255, 255, 255, 0.95)',
    backdropFilter: scrolled ? 'blur(15px)' : 'blur(10px)',
    boxShadow: scrolled ? '0 4px 25px rgba(0, 0, 0, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.1)',
    paddingTop: scrolled ? '0px' : '1px',
    paddingBottom: scrolled ? '0px' : '1px',
    transition: 'all 0.3s ease',
    borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
  };

  const logoImgStyle = {
    height: '50px',
    filter: 'drop-shadow(0 4px 8px rgba(0, 160, 233, 0.3))',
    transition: 'all 0.3s ease',
  };

  const navLinkStyle = (isActive) => ({
    color: '#002956',
    fontWeight: 600,
    fontSize: '15px',
    letterSpacing: '0.3px',
    padding: '8px 10px',
    position: 'relative',
    textDecoration: 'none',
    display: 'block',
    whiteSpace: 'nowrap',
    ...(isActive && { color: '#00a0e9' }),
  });

  const dropdownMenuStyle = (isOpen) => ({
    background: 'rgba(0, 41, 86, 0.98)',
    backdropFilter: 'blur(15px)',
    borderRadius: '12px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    marginTop: '12px',
    border: '1px solid rgba(255, 255, 255, 0.15)',
    minWidth: '280px',
    display: 'block', // Always block for transitions
    visibility: isOpen ? 'visible' : 'hidden',
    opacity: isOpen ? 1 : 0,
    transform: isOpen ? 'translateY(0px) translateX(-50%)' : 'translateY(-10px) translateX(-50%)',
    transition: `opacity 0.3s ease, transform 0.3s ease, visibility 0s linear ${isOpen ? '0s' : '0.3s'}`,
    padding: '8px 0',
    position: 'absolute',
    top: '100%',
    left: '50%',
    zIndex: 1000
  });

  const dropdownItemStyle = (isActive) => ({
    color: '#ffffff',
    padding: '7px 18px', // Reduced padding
    display: 'flex',
    alignItems: 'center',
    fontSize: '15px', // Slightly reduced font size for compactness
    fontWeight: 300,
    textDecoration: 'none',
    borderLeft: isActive ? '3px solid #00a0e9' : '3px solid transparent',
    backgroundColor: isActive ? 'rgba(0, 160, 233, 0.15)' : 'transparent',
    transition: 'all 0.2s ease',
    // ':hover': { // Hover styles in React inline styles need a different approach (e.g. state or CSS classes)
    //   backgroundColor: 'rgba(0, 160, 233, 0.15)',
    //   color: '#00a0e9',
    //   borderLeft: '3px solid #00a0e9'
    // }
    // For hover, you might want to use CSS or onMouseEnter/onMouseLeave handlers to change styles
  });
  
  // Helper for hover on dropdown items if you can't use CSS classes easily
  const getDropdownItemStyleWithHover = (isActive, isHovered) => {
    const baseStyle = dropdownItemStyle(isActive);
    if (isHovered) {
      return {
        ...baseStyle,
        backgroundColor: 'rgba(0, 160, 233, 0.15)',
        color: '#00a0e9', // Ensure text color changes on hover if needed
        borderLeft: '3px solid #00a0e9'
      };
    }
    return baseStyle;
  };


  const mobileNavStyle = {
    position: 'fixed',
    top: '75px',
    left: 0,
    right: 0,
    background: 'rgba(0, 41, 86, 0.98)',
    backdropFilter: 'blur(15px)',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    padding: '25px',
    borderBottomLeftRadius: '20px',
    borderBottomRightRadius: '20px',
    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
    zIndex: 999,
  };

  const mobileDropdownMenuStyle = { // Styles for dropdowns when in mobile view
    background: 'rgba(0, 160, 233, 0.05)', // More subtle background for mobile nested
    borderRadius: '8px', // Slightly smaller radius
    marginTop: '10px',
    marginBottom: '5px',
    border: '1px solid rgba(0, 160, 233, 0.15)',
  };

  const initialNestedSubmenuStyle = {
    display: "block",
    opacity: "0",
    visibility: "hidden",
    transform: 'translateX(-10px)', // Slide from left for LTR, could be dynamic
    transition: closeTransition,
    position: "absolute",
    top: "0px", // Align with the top of the parent item
    left: "100%", // Position to the right of the parent item
    marginLeft: "5px", // Small gap from parent
    borderRadius: "10px", // Consistent rounding
    boxShadow: "0 8px 25px rgba(0, 0, 0, 0.25)",
    minWidth: "240px", // Adjusted minWidth
    zIndex: 1050,
    backgroundColor: 'rgba(0, 35, 70, 0.98)', // Slightly different from main for depth
    color: '#ffffff',
    padding: '6px 0', // Reduced padding
    border: '1px solid rgba(255, 255, 255, 0.1)',
  };
  
  const closeAllSubmenus = (parentElement) => {
    const openSubmenus = parentElement.querySelectorAll('.dropdown-menu[style*="visibility: visible"]');
    openSubmenus.forEach(menu => {
        menu.style.transition = closeTransition;
        menu.style.opacity = '0';
        menu.style.visibility = 'hidden';
        menu.style.transform = 'translateX(-10px)';
    });
  };

  return (
    <header
      className="navbar navbar-expand-lg sticky-top"
      style={headerStyle}
      ref={headerRef}
    >
      <div className="container-fluid px-3 px-lg-5">
        <Link to="/" className="navbar-brand me-auto" ref={logoRef}>
          <img src={MakonisLogo} alt="Makonis Software" style={logoImgStyle} />
        </Link>

        <button
          className="navbar-toggler"
          type="button"
          aria-expanded={mobileMenuOpen}
          aria-label="Toggle navigation menu"
          onClick={toggleMobileMenu}
          style={{ color: '#002956', border: 'none', fontSize: '24px' }}
        >
          <i className={`fas ${mobileMenuOpen ? "fa-times" : "fa-bars"}`}></i>
        </button>

        <div
          className={`collapse navbar-collapse ${mobileMenuOpen ? 'show' : ''}`}
          id="navbarNavDropdown"
          style={mobileMenuOpen ? mobileNavStyle : {}}
        >
          <ul className="navbar-nav ms-auto mb-2 mb-lg-0 align-items-lg-center">
            {navItems.map((item) => (
              <li
                key={item.name}
                className={`nav-item-gsap nav-item ${item.isDropdown ? "dropdown" : ""} ${
                  (item.name === "Who we are" && aboutDropdownOpen) || (item.name === "Business" && businessDropdownOpen)
                    ? "show"
                    : ""
                }`}
                ref={item.name === "Who we are" ? aboutDropdownRef : item.name === "Business" ? businessDropdownRef : null}
              >
                {item.isDropdown ? (
                  <>
                    <a
                      href={item.path} // Keep href for SEO, but preventDefault for manual toggle
                      className={`nav-link d-flex align-items-center ${item.name === "Who we are" ? 'who-we-are-dropdown-toggle' : item.name === "Business" ? 'business-dropdown-toggle' : ''}`}
                      style={{
                        ...navLinkStyle(location.pathname.startsWith(item.path) && item.path !== "/#about" && item.path !== "/#business"),
                      }}
                      id={`${item.name.replace(/\s+/g, '')}Dropdown`}
                      role="button"
                      aria-expanded={(item.name === "Who we are" && aboutDropdownOpen) || (item.name === "Business" && businessDropdownOpen)}
                      onClick={(e) => {
                        e.preventDefault();
                        if (item.name === "Who we are") {
                          setBusinessDropdownOpen(false);
                          setAboutDropdownOpen(!aboutDropdownOpen);
                        } else if (item.name === "Business") {
                          setAboutDropdownOpen(false);
                          setBusinessDropdownOpen(!businessDropdownOpen);
                        }
                      }}
                    >
                      <span>{item.name}</span>
                      <i
                        className="fas fa-chevron-down ms-2"
                        style={{
                          fontSize: '12px',
                          transition: 'transform 0.3s ease',
                          transform: (item.name === "Who we are" && aboutDropdownOpen) || (item.name === "Business" && businessDropdownOpen) ? 'rotate(180deg)' : 'rotate(0deg)',
                        }}
                      />
                    </a>
                    <ul
                      className="dropdown-menu"
                      aria-labelledby={`${item.name.replace(/\s+/g, '')}Dropdown`}
                      style={{
                        ...dropdownMenuStyle(
                          (item.name === "Who we are" && aboutDropdownOpen) ||
                          (item.name === "Business" && businessDropdownOpen)
                        ),
                        ...(mobileMenuOpen && mobileDropdownMenuStyle)
                      }}
                    >
                      {item.dropdownItems.map((dropdownItem) => (
                        <li key={dropdownItem.name} className={dropdownItem.isDropdown ? "dropdown-submenu" : ""} style={{ position: 'relative', padding: 0 /* Let Link control padding */ }}>
                          {dropdownItem.isDropdown ? (
                            <>
                              <a
                                href={dropdownItem.path} // Keep href for SEO
                                className="dropdown-item d-flex align-items-center justify-content-between" // Added justify-content-between
                                style={dropdownItemStyle(location.pathname === dropdownItem.path)} // Use base style
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const submenu = e.currentTarget.nextElementSibling;
                                  const parentMenu = e.currentTarget.closest('.dropdown-menu');
                                  
                                  // Close other submenus at the same level within the same parent
                                  if (parentMenu) {
                                    const siblings = Array.from(parentMenu.children).map(li => li.querySelector('.dropdown-menu'));
                                    siblings.forEach(siblingSubmenu => {
                                        if (siblingSubmenu && siblingSubmenu !== submenu && siblingSubmenu.style.visibility === 'visible') {
                                            siblingSubmenu.style.transition = closeTransition;
                                            siblingSubmenu.style.opacity = '0';
                                            siblingSubmenu.style.visibility = 'hidden';
                                            siblingSubmenu.style.transform = 'translateX(-10px)';
                                        }
                                    });
                                  }

                                  const isOpen = submenu.style.opacity === '1';
                                  if (isOpen) {
                                    submenu.style.transition = closeTransition;
                                    submenu.style.opacity = '0';
                                    submenu.style.visibility = 'hidden';
                                    submenu.style.transform = 'translateX(-10px)';
                                  } else {
                                    submenu.style.transition = openTransition;
                                    submenu.style.visibility = 'visible';
                                    submenu.style.opacity = '1';
                                    submenu.style.transform = 'translateX(0px)';
                                  }
                                }}
                              >
                                <span>
                                  {dropdownItem.icon && <i className={`fas ${dropdownItem.icon} me-2`}></i>}
                                  {dropdownItem.name}
                                </span>
                                <i className="fas fa-chevron-right" style={{ fontSize: '10px', marginLeft: 'auto' }}></i> {/* Submenu indicator */}
                              </a>
                              <ul className="dropdown-menu" style={initialNestedSubmenuStyle}>
                                {dropdownItem.dropdownItems.map((subItem) => (
                                  <li key={subItem.name} style={{ padding: '0' }}>
                                    <Link
                                      className="dropdown-item"
                                      to={subItem.path}
                                      style={{ ...dropdownItemStyle(location.pathname === subItem.path), color: '#ffffff' }}
                                      onClick={(e) => {
                                        // e.stopPropagation(); // Optional: if you don't want parent dropdowns to know about this click immediately
                                        setAboutDropdownOpen(false);
                                        setBusinessDropdownOpen(false);
                                        setMobileMenuOpen(false);
                                        // Manually close all submenus
                                        const allMainMenus = document.querySelectorAll('#navbarNavDropdown > ul > li.dropdown > ul.dropdown-menu');
                                        allMainMenus.forEach(mainMenu => closeAllSubmenus(mainMenu));
                                      }}
                                    >
                                      {subItem.name}
                                    </Link>
                                  </li>
                                ))}
                              </ul>
                            </>
                          ) : (
                            <Link
                              className="dropdown-item"
                              to={dropdownItem.path}
                              style={dropdownItemStyle(location.pathname === dropdownItem.path)}
                              onClick={() => {
                                setAboutDropdownOpen(false);
                                setBusinessDropdownOpen(false);
                                setMobileMenuOpen(false);
                              }}
                            >
                              {dropdownItem.icon && <i className={`fas ${dropdownItem.icon} me-2`}></i>}
                              {dropdownItem.name}
                              {dropdownItem.note && (
                                <small style={{ display: "block", fontWeight: "normal", fontSize: "12px", marginTop: "3px", color: "#ccc" }}>
                                  {dropdownItem.note}
                                </small>
                              )}
                            </Link>
                          )}
                        </li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <Link
                    className={`nav-link ${location.pathname === item.path ? "active" : ""}`}
                    to={item.path}
                    style={navLinkStyle(location.pathname === item.path)}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </header>
  );
};

export default Header;