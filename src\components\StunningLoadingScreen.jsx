import React from 'react';

import <PERSON>konisLogo from '../Asserts/Makonis-Logo.png';

const EnhancedMakonisLoadingScreen = React.forwardRef(({ loadingProgress }, ref) => {
  // Most styles are kept from your "StunningLoadingScreen"
  // Minor adjustments might be made, especially around '.stunning-logo-face'
  const styles = `
    .stunning-loader-container {
      background: linear-gradient(135deg, #001f3f 0%, #00152b 50%, #000c1a 100%);
      z-index: 99999;
      overflow: hidden; /* Prevent scrollbars from temporary large elements */
    }

    .stunning-grid-bg {
      background-image:
        linear-gradient(rgba(0, 120, 200, 0.07) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 120, 200, 0.07) 1px, transparent 1px);
      background-size: 70px 70px;
      animation: stunningGridMove 40s linear infinite;
      opacity: 0.5;
    }
    @keyframes stunningGridMove {
      0% { background-position: 0 0; }
      100% { background-position: 140px 140px; }
    }

    ${[...Array(7)].map((_, i) => `
      .stunning-orb-${i} {
        width: ${100 + i * 30}px;
        height: ${100 + i * 30}px;
        background: radial-gradient(circle, rgba(0, 160, 233, ${0.05 - i * 0.005}) 0%, transparent 65%);
        filter: blur(${20 + i * 5}px);
        animation: stunningFloatOrb${i} ${12 + i * 3}s ease-in-out infinite;
        animation-delay: ${i * 0.7}s;
        pointer-events: none; /* Orbs should not interfere with interaction */
      }
      @keyframes stunningFloatOrb${i} {
        0%, 100% {
          transform: translate(${Math.random() * 20 - 10}px, ${Math.random() * 20 - 10}px) scale(1);
          opacity: ${0.04 - i * 0.005};
        }
        50% {
          transform: translate(${Math.random() * 60 - 30}px, ${Math.random() * 60 - 30}px) scale(${1 + i * 0.03});
          opacity: ${0.06 - i * 0.005};
        }
      }
    `).join('')}

    .stunning-logo-perspective {
      perspective: 1500px;
    }

    .stunning-logo-3d-container {
      width: 280px; /* Slightly increased to better fit SVG and rings */
      height: 280px; /* Slightly increased */
      position: relative;
      margin: 0 auto;
      transform-style: preserve-3d;
      /* animation: stunningLogoContainerRotate 45s linear infinite alternate; */ /* Optional: subtle rotation for the whole 3D container */
    }
    /* @keyframes stunningLogoContainerRotate {
      0% { transform: rotateY(-10deg) rotateX(5deg); }
      100% { transform: rotateY(10deg) rotateX(-5deg); }
    } */


    .stunning-globe {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 300px; /* Can be same or slightly larger than 3d container */
      height: 300px;
      margin-top: -150px;
      margin-left: -150px;
      border-radius: 50%;
      background: radial-gradient(circle at center, #007acc 0%, #004a80 70%);
      box-shadow:
        inset 0 0 30px #00aaff,
        0 0 40px #00aaff;
      animation: stunningGlobeRotate 25s linear infinite; /* Slightly adjusted timing */
      z-index: 0;
    }
    @keyframes stunningGlobeRotate {
      0% { transform: rotateY(0deg) rotateX(0deg); }
      100% { transform: rotateY(360deg) rotateX(360deg); }
    }

    .stunning-logo-face {
      /* background: radial-gradient(circle at 50% 40%, rgba(200, 235, 255, 0.1) 0%, rgba(150, 210, 240, 0.05) 100%); */
      /* border: 1px solid rgba(0, 180, 255, 0.3); */
      /* box-shadow:
        0 0 30px rgba(0, 180, 255, 0.2),
        inset 0 0 10px rgba(0, 150, 220, 0.1); */
      /* Removed background and border to let SVG be primary. Adjusted shadow. */
      position: absolute;
      width: 100%;
      height: 100%;
      /* border-radius: 50%; */ /* Removed to accommodate rectangular SVG better */
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stunning-logo-front { transform: translateZ(40px); }
    /* .stunning-logo-back can be removed if not needed, or display a different variant of the SVG animation */
    .stunning-logo-back {
      transform: translateZ(-40px) rotateY(180deg);
      /* If you want the SVG on the back too, you'd put <MakonisLogo /> here as well,
         possibly with different animation props or a simpler static version.
         For now, let's keep it simple and focus on the front. */
    }

    /* .stunning-logo-image related styles are removed as we use SVG component now */

    ${[...Array(5)].map((_, i) => ` /* Increased number of rings for more depth */
      .stunning-logo-ring-${i} {
        width: ${220 + i * 40}px; /* Adjusted sizes for more spread */
        height: ${220 + i * 40}px;
        border: 2.5px solid rgba(0, 180, 255, ${0.35 - i * 0.06}); /* Adjusted opacity */
        box-shadow: 0 0 ${12 + i * 6}px rgba(0, 180, 255, ${0.25 - i * 0.04}); /* Adjusted shadow */
        animation: stunningRingRotate${i} ${8 + i * 2.5}s linear infinite; /* Adjusted speed */
        animation-delay: ${i * 0.4}s;
        position: absolute;
        top: 50%;
        left: 50%;
        border-radius: 50%;
        transform-style: preserve-3d;
        transform-origin: center center;
        margin: 0; /* Reset margin */
      }
      @keyframes stunningRingRotate${i} {
        0% { transform: translate(-50%, -50%) rotateX(${15 + i * 15}deg) translateZ(${i * 10}px) rotateY(0deg); } /* Adjusted angles */
        100% { transform: translate(-50%, -50%) rotateX(${15 + i * 15}deg) translateZ(${i * 10}px) rotateY(${i % 2 === 0 ? '360deg' : '-360deg'}); }
      }
    `).join('')}

    .stunning-loading-title {
      font-weight: 700;
      letter-spacing: 3px;
      font-size: 1.8rem;
      color: #e0f5ff;
      text-shadow: 0 0 10px rgba(0, 180, 255, 0.6), 0 0 20px rgba(0, 180, 255, 0.4);
      animation: stunningTextPulse 2.8s ease-in-out infinite;
    }
    @keyframes stunningTextPulse {
      0%, 100% { opacity: 0.8; transform: scale(1); }
      50% { opacity: 1; transform: scale(1.03); text-shadow: 0 0 15px rgba(0, 200, 255, 0.8), 0 0 30px rgba(0, 200, 255, 0.6); }
    }

    .stunning-loading-status {
      color: rgba(180, 220, 255, 0.8);
      font-size: 1rem;
      letter-spacing: 1.5px;
      animation: stunningFadeInOut 3s ease-in-out infinite;
      min-height: 1.5em; /* Ensure space for text */
    }
    @keyframes stunningFadeInOut {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }

    .stunning-progress-bar-container {
      width: 350px;
      height: 6px;
      background: rgba(0, 50, 100, 0.3);
      border-radius: 3px;
      box-shadow: inset 0 1px 2px rgba(0,0,0,0.3);
      margin: 0 auto 0.5rem auto; /* Consistent margin */
    }

    .stunning-progress-bar-fill {
      height: 100%;
      background: linear-gradient(90deg, #00a0e9 0%, #00d0ff 50%, #00a0e9 100%);
      background-size: 250% 100%;
      border-radius: 3px;
      box-shadow: 0 0 15px rgba(0, 180, 255, 0.8), 0 0 8px rgba(100, 220, 255, 0.6);
      animation: stunningProgressBarFill 2s linear infinite;
      transition: width 0.3s ease-out; /* Smooth progress bar update */
    }

    @keyframes stunningProgressBarFill {
      0% { background-position: 0% 50%; }
      100% { background-position: 250% 50%; }
    }

    .stunning-progress-text {
      color: rgba(200, 235, 255, 0.9);
      font-size: 0.95rem;
      font-weight: 500;
      letter-spacing: 1.5px;
      text-shadow: 0 0 5px rgba(0,160,233,0.5);
      margin: 0; /* Reset margin */
    }
  `;

  return (
    <>
      <style>{styles}</style>
      <div
        ref={ref}
        className="stunning-loader-container position-fixed top-0 start-0 w-100 h-100 d-flex flex-column align-items-center justify-content-center" // Added flex-column
      >
        <div className="stunning-grid-bg position-absolute w-100 h-100" />

        {[...Array(7)].map((_, i) => (
          <div
            key={`orb-${i}`}
            className={`stunning-orb-${i} position-absolute rounded-circle`}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
          />
        ))}

        {/* Main content area, centered */}
        <div className="text-center position-relative" style={{ zIndex: 1 }}>
          <div className="stunning-globe" />

          <div className="stunning-logo-perspective mb-4"> {/* Adjusted margin */}
            <div className="stunning-logo-3d-container">
              <div className="stunning-logo-face stunning-logo-front">
                {/* Integrate the Framer Motion SVG Logo Animation Here */}
                <img src={MakonisLogo} alt="Makonis Logo" />
              </div>
              {/* Back face can be omitted or also contain MakonisLogo if desired */}
              {/* <div className="stunning-logo-face stunning-logo-back">
                <MakonisLogo /> // Or a static version
              </div> */}

              {[...Array(5)].map((_, i) => (
                <div key={`ring-${i}`} className={`stunning-logo-ring-${i}`} />
              ))}
            </div>
          </div>

          <h3 className="stunning-loading-title mb-3">MAKONIS SOFTWARE</h3>

          <div className="stunning-loading-status mb-3"> {/* Adjusted margin */}
            {loadingProgress < 20 && "Initializing Core Systems..."}
            {loadingProgress >= 20 && loadingProgress < 40 && "Loading Interface Modules..."}
            {loadingProgress >= 40 && loadingProgress < 60 && "Assembling Components..."}
            {loadingProgress >= 60 && loadingProgress < 80 && "Finalizing Setup..."}
            {loadingProgress >= 80 && "Almost Ready to Launch..."}
          </div>

          <div className="stunning-progress-bar-container">
            <div className="stunning-progress-bar-fill" style={{ width: `${loadingProgress}%` }} />
          </div>

          <p className="stunning-progress-text mt-2">{loadingProgress}%</p> {/* Adjusted margin */}
        </div>
      </div>
    </>
  );
});

export default EnhancedMakonisLoadingScreen;