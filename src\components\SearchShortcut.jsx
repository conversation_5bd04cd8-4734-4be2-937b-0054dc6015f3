import React, { useEffect } from 'react';
import { useSearch } from '../contexts/SearchContext';

const SearchShortcut = () => {
  const { openSearch, isSearchOpen } = useSearch();

  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+K or Cmd+K to open search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        if (!isSearchOpen) {
          openSearch();
        }
      }
      
      // Forward slash (/) to open search (like GitHub)
      if (event.key === '/' && !isSearchOpen) {
        // Only if not typing in an input field
        const activeElement = document.activeElement;
        const isInputField = activeElement && (
          activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.contentEditable === 'true'
        );
        
        if (!isInputField) {
          event.preventDefault();
          openSearch();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [openSearch, isSearchOpen]);

  return null; // This component doesn't render anything
};

export default SearchShortcut;
