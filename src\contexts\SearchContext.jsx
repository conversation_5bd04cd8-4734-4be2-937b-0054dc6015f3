import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { 
  performSearch, 
  getSearchSuggestions, 
  getPopularSearches,
  getSearchResultsByCategory,
  trackSearch,
  debounce 
} from '../utils/searchUtils';

// Create Search Context
const SearchContext = createContext();

// Custom hook to use search context
export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

// Search Provider Component
export const SearchProvider = ({ children }) => {
  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState([]);
  const [searchHistory, setSearchHistory] = useState([]);

  // Refs for managing focus and keyboard navigation
  const searchInputRef = useRef(null);
  const searchResultsRef = useRef(null);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      if (query.trim().length >= 2) {
        setIsSearching(true);
        try {
          const results = performSearch(query, { maxResults: 10 });
          setSearchResults(results.results);
          
          // Get suggestions for autocomplete
          const searchSuggestions = getSearchSuggestions(query, 5);
          setSuggestions(searchSuggestions);
          
          // Track search
          trackSearch(query, results.totalResults);
        } catch (error) {
          console.error('Search error:', error);
          setSearchResults([]);
          setSuggestions([]);
        } finally {
          setIsSearching(false);
        }
      } else if (query.trim().length === 0) {
        // Show popular searches when query is empty
        const popularSearches = getPopularSearches();
        setSuggestions(popularSearches);
        setSearchResults([]);
        setIsSearching(false);
      } else {
        setSearchResults([]);
        setSuggestions([]);
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Handle search input change
  const handleSearchChange = useCallback((query) => {
    setSearchQuery(query);
    setSelectedSuggestionIndex(-1);
    debouncedSearch(query);
  }, [debouncedSearch]);

  // Handle search submission
  const handleSearchSubmit = useCallback((query = searchQuery) => {
    if (!query.trim()) return;

    const results = performSearch(query, { maxResults: 20 });
    setSearchResults(results.results);
    
    // Add to search history
    const newSearch = {
      id: Date.now(),
      query: query.trim(),
      timestamp: new Date(),
      resultCount: results.totalResults
    };
    
    setSearchHistory(prev => [newSearch, ...prev.slice(0, 9)]); // Keep last 10 searches
    setRecentSearches(prev => {
      const filtered = prev.filter(search => search.query !== query.trim());
      return [query.trim(), ...filtered].slice(0, 5); // Keep last 5 unique searches
    });

    // Track search submission
    trackSearch(query, results.totalResults);
  }, [searchQuery]);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults([]);
    setSuggestions([]);
    setSelectedSuggestionIndex(-1);
    setIsSearching(false);
    
    // Show popular searches
    const popularSearches = getPopularSearches();
    setSuggestions(popularSearches);
  }, []);

  // Open search
  const openSearch = useCallback(() => {
    setIsSearchOpen(true);
    // Show popular searches when opening
    if (!searchQuery) {
      const popularSearches = getPopularSearches();
      setSuggestions(popularSearches);
    }
    // Focus search input after a brief delay
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 100);
  }, [searchQuery]);

  // Close search
  const closeSearch = useCallback(() => {
    setIsSearchOpen(false);
    setSelectedSuggestionIndex(-1);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event) => {
    const { key } = event;
    const suggestionCount = suggestions.length;

    switch (key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestionCount - 1 ? prev + 1 : 0
        );
        break;
      
      case 'ArrowUp':
        event.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestionCount - 1
        );
        break;
      
      case 'Enter':
        event.preventDefault();
        if (selectedSuggestionIndex >= 0 && suggestions[selectedSuggestionIndex]) {
          const selectedSuggestion = suggestions[selectedSuggestionIndex];
          setSearchQuery(selectedSuggestion.title);
          handleSearchSubmit(selectedSuggestion.title);
        } else {
          handleSearchSubmit();
        }
        break;
      
      case 'Escape':
        event.preventDefault();
        if (searchQuery) {
          clearSearch();
        } else {
          closeSearch();
        }
        break;
      
      default:
        break;
    }
  }, [suggestions, selectedSuggestionIndex, searchQuery, handleSearchSubmit, clearSearch, closeSearch]);

  // Select suggestion
  const selectSuggestion = useCallback((suggestion) => {
    setSearchQuery(suggestion.title);
    handleSearchSubmit(suggestion.title);
    setSelectedSuggestionIndex(-1);
    
    // Track suggestion selection
    trackSearch(suggestion.title, 1, suggestion);
  }, [handleSearchSubmit]);

  // Get categorized search results
  const getCategorizedResults = useCallback((query = searchQuery) => {
    return getSearchResultsByCategory(query);
  }, [searchQuery]);

  // Search context value
  const contextValue = {
    // State
    searchQuery,
    searchResults,
    suggestions,
    isSearching,
    isSearchOpen,
    selectedSuggestionIndex,
    recentSearches,
    searchHistory,
    
    // Refs
    searchInputRef,
    searchResultsRef,
    
    // Actions
    handleSearchChange,
    handleSearchSubmit,
    clearSearch,
    openSearch,
    closeSearch,
    handleKeyDown,
    selectSuggestion,
    getCategorizedResults,
    
    // Utilities
    setSelectedSuggestionIndex
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
};
