import React, { useEffect, useState } from 'react';
import { Container, Row, Col, Card, Badge, Button, Form } from 'react-bootstrap';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { useSearch } from '../contexts/SearchContext';
import { getTypeIcon, getTypeDisplayName, getSearchResultsByCategory } from '../utils/searchUtils';

const SearchResultsPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { searchQuery, handleSearchChange, getCategorizedResults } = useSearch();
  
  const [results, setResults] = useState({});
  const [activeFilter, setActiveFilter] = useState('all');
  const [sortBy, setSortBy] = useState('relevance');
  
  const queryFromUrl = searchParams.get('q') || '';

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    if (queryFromUrl && queryFromUrl !== searchQuery) {
      handleSearchChange(queryFromUrl);
    }
  }, [queryFromUrl, searchQuery, handleSearchChange]);

  useEffect(() => {
    if (searchQuery) {
      const categorizedResults = getCategorizedResults(searchQuery);
      setResults(categorizedResults);
    }
  }, [searchQuery, getCategorizedResults]);

  // Get total results count
  const getTotalResults = () => {
    return Object.values(results).reduce((total, category) => total + category.length, 0);
  };

  // Get filtered results based on active filter
  const getFilteredResults = () => {
    if (activeFilter === 'all') {
      return results;
    }
    return { [activeFilter]: results[activeFilter] || [] };
  };

  // Handle new search from this page
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const newQuery = formData.get('search');
    if (newQuery.trim()) {
      navigate(`/search-results?q=${encodeURIComponent(newQuery.trim())}`);
    }
  };

  const filteredResults = getFilteredResults();
  const totalResults = getTotalResults();

  return (
    <div style={{ 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      paddingTop: '100px'
    }}>
      <Container>
        {/* Search Header */}
        <Row className="mb-4">
          <Col>
            <Card className="shadow-lg border-0" style={{ borderRadius: '15px' }}>
              <Card.Body className="p-4">
                <h1 className="h3 mb-3" style={{ color: '#002956' }}>
                  Search Results
                </h1>
                
                {/* Search Form */}
                <Form onSubmit={handleSearchSubmit} className="mb-3">
                  <div className="d-flex">
                    <Form.Control
                      type="text"
                      name="search"
                      placeholder="Search services, team, technologies..."
                      defaultValue={queryFromUrl}
                      className="me-2"
                      style={{ borderRadius: '25px' }}
                    />
                    <Button 
                      type="submit" 
                      variant="primary"
                      style={{ 
                        borderRadius: '25px',
                        background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
                        border: 'none'
                      }}
                    >
                      <i className="fas fa-search"></i>
                    </Button>
                  </div>
                </Form>

                {searchQuery && (
                  <div className="d-flex justify-content-between align-items-center">
                    <p className="mb-0 text-muted">
                      {totalResults} result{totalResults !== 1 ? 's' : ''} for 
                      <strong> "{searchQuery}"</strong>
                    </p>
                    
                    <Form.Select
                      size="sm"
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      style={{ width: 'auto' }}
                    >
                      <option value="relevance">Sort by Relevance</option>
                      <option value="alphabetical">Sort Alphabetically</option>
                      <option value="type">Sort by Type</option>
                    </Form.Select>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {searchQuery ? (
          <>
            {/* Filter Tabs */}
            {totalResults > 0 && (
              <Row className="mb-4">
                <Col>
                  <div className="d-flex flex-wrap gap-2">
                    <Button
                      variant={activeFilter === 'all' ? 'primary' : 'outline-primary'}
                      size="sm"
                      onClick={() => setActiveFilter('all')}
                      className="rounded-pill"
                    >
                      All ({totalResults})
                    </Button>
                    
                    {Object.entries(results).map(([category, items]) => (
                      items.length > 0 && (
                        <Button
                          key={category}
                          variant={activeFilter === category ? 'primary' : 'outline-primary'}
                          size="sm"
                          onClick={() => setActiveFilter(category)}
                          className="rounded-pill"
                        >
                          <i className={`fas ${getTypeIcon(items[0]?.type)} me-1`}></i>
                          {getTypeDisplayName(items[0]?.type)} ({items.length})
                        </Button>
                      )
                    ))}
                  </div>
                </Col>
              </Row>
            )}

            {/* Search Results */}
            {totalResults > 0 ? (
              Object.entries(filteredResults).map(([category, items]) => (
                items.length > 0 && (
                  <Row key={category} className="mb-4">
                    <Col>
                      {activeFilter === 'all' && (
                        <h4 className="text-white mb-3">
                          <i className={`fas ${getTypeIcon(items[0]?.type)} me-2`}></i>
                          {getTypeDisplayName(items[0]?.type)}
                        </h4>
                      )}
                      
                      <Row>
                        {items.map((result) => (
                          <Col key={result.id} md={6} lg={4} className="mb-3">
                            <Card 
                              className="h-100 shadow-sm border-0 search-result-card"
                              style={{ 
                                borderRadius: '12px',
                                transition: 'all 0.3s ease',
                                cursor: 'pointer'
                              }}
                              onClick={() => navigate(result.path)}
                            >
                              <Card.Body className="p-3">
                                <div className="d-flex align-items-start mb-2">
                                  <div 
                                    className="me-3 d-flex align-items-center justify-content-center"
                                    style={{
                                      width: '40px',
                                      height: '40px',
                                      background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
                                      borderRadius: '8px',
                                      color: 'white',
                                      fontSize: '16px',
                                      flexShrink: 0
                                    }}
                                  >
                                    <i className={`fas ${getTypeIcon(result.type)}`}></i>
                                  </div>
                                  
                                  <div className="flex-grow-1">
                                    <h6 
                                      className="mb-1"
                                      dangerouslySetInnerHTML={{ 
                                        __html: result.highlightedTitle || result.title 
                                      }}
                                    />
                                    <Badge 
                                      bg="light" 
                                      text="dark" 
                                      className="small"
                                    >
                                      {getTypeDisplayName(result.type)}
                                    </Badge>
                                  </div>
                                </div>
                                
                                <p 
                                  className="text-muted small mb-2"
                                  dangerouslySetInnerHTML={{ 
                                    __html: result.highlightedDescription || result.description 
                                  }}
                                  style={{
                                    display: '-webkit-box',
                                    WebkitLineClamp: 3,
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden'
                                  }}
                                />
                                
                                <Link 
                                  to={result.path}
                                  className="text-decoration-none small"
                                  style={{ color: '#00a0e9' }}
                                >
                                  View Details <i className="fas fa-arrow-right ms-1"></i>
                                </Link>
                              </Card.Body>
                            </Card>
                          </Col>
                        ))}
                      </Row>
                    </Col>
                  </Row>
                )
              ))
            ) : (
              <Row>
                <Col>
                  <Card className="text-center shadow-lg border-0" style={{ borderRadius: '15px' }}>
                    <Card.Body className="p-5">
                      <i className="fas fa-search-minus mb-3" style={{ fontSize: '48px', color: '#adb5bd' }}></i>
                      <h4 className="mb-3">No Results Found</h4>
                      <p className="text-muted mb-4">
                        We couldn't find any results for "<strong>{searchQuery}</strong>". 
                        Try different keywords or check your spelling.
                      </p>
                      <div className="d-flex flex-wrap justify-content-center gap-2">
                        <Button variant="outline-primary" size="sm" onClick={() => handleSearchChange('testing')}>
                          Testing
                        </Button>
                        <Button variant="outline-primary" size="sm" onClick={() => handleSearchChange('ai')}>
                          AI
                        </Button>
                        <Button variant="outline-primary" size="sm" onClick={() => handleSearchChange('iot')}>
                          IoT
                        </Button>
                        <Button variant="outline-primary" size="sm" onClick={() => handleSearchChange('team')}>
                          Team
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}
          </>
        ) : (
          <Row>
            <Col>
              <Card className="text-center shadow-lg border-0" style={{ borderRadius: '15px' }}>
                <Card.Body className="p-5">
                  <i className="fas fa-search mb-3" style={{ fontSize: '48px', color: '#00a0e9' }}></i>
                  <h4 className="mb-3">Start Your Search</h4>
                  <p className="text-muted">
                    Enter a search term above to find services, team members, technologies, and more.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </Container>

      <style jsx>{`
        .search-result-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        }
      `}</style>
    </div>
  );
};

export default SearchResultsPage;
