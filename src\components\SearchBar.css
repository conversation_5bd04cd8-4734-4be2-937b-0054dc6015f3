/* Search Bar Styles */
.search-bar-container {
  width: 100%;
  max-width: 500px;
}

.search-input {
  border: 2px solid #e1e5e9;
  border-radius: 25px;
  padding: 12px 20px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.search-input:focus {
  border-color: #00a0e9;
  box-shadow: 0 0 0 0.2rem rgba(0, 160, 233, 0.25);
  background: #ffffff;
}

.search-icon {
  background: transparent;
  border: none;
  color: #6c757d;
  padding-left: 15px;
}

.search-clear {
  background: transparent;
  border: none;
  color: #6c757d;
  transition: color 0.2s ease;
  padding-right: 15px;
}

.search-clear:hover {
  color: #dc3545;
}

/* Search Dropdown Styles */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  z-index: 1050;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 8px;
  backdrop-filter: blur(20px);
  border-top: 3px solid #00a0e9;
}

.search-dropdown-header {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 12px 12px 0 0;
}

.search-dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f3f4;
}

.search-dropdown-item:hover,
.search-dropdown-item.selected {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-left: 3px solid #00a0e9;
  padding-left: 13px;
}

.search-dropdown-item:last-child {
  border-bottom: none;
  border-radius: 0 0 12px 12px;
}

.search-item-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
  color: white;
  border-radius: 8px;
  margin-right: 12px;
  font-size: 16px;
  flex-shrink: 0;
}

.search-dropdown-item.selected .search-item-icon {
  background: linear-gradient(135deg, #0056b3 0%, #003d82 100%);
  transform: scale(1.05);
}

.search-item-content {
  flex: 1;
  min-width: 0;
}

.search-item-title {
  font-size: 15px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-item-type {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.search-item-arrow {
  color: #adb5bd;
  font-size: 12px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-dropdown-item:hover .search-item-arrow,
.search-dropdown-item.selected .search-item-arrow {
  color: #00a0e9;
  transform: translateX(3px);
}

.search-dropdown-footer {
  padding: 8px 16px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.search-view-all {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #00a0e9;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.search-view-all:hover {
  background: #e8f4fd;
  color: #0056b3;
}

.search-dropdown-empty {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.search-dropdown-empty i {
  font-size: 32px;
  color: #adb5bd;
  display: block;
  margin-bottom: 12px;
}

/* Search highlighting */
.search-highlight {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-bar-container {
    max-width: 100%;
  }
  
  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 16px;
  }
  
  .search-dropdown {
    max-height: 300px;
    margin-top: 4px;
  }
  
  .search-dropdown-item {
    padding: 10px 12px;
  }
  
  .search-item-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
    margin-right: 10px;
  }
  
  .search-item-title {
    font-size: 14px;
  }
  
  .search-item-type {
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .search-input {
    background: rgba(33, 37, 41, 0.95);
    border-color: #495057;
    color: #ffffff;
  }
  
  .search-input:focus {
    background: #212529;
    border-color: #00a0e9;
  }
  
  .search-dropdown {
    background: #212529;
    border-color: #495057;
  }
  
  .search-dropdown-header {
    background: #343a40;
    color: #ffffff;
    border-bottom-color: #495057;
  }
  
  .search-dropdown-item {
    border-bottom-color: #343a40;
  }
  
  .search-dropdown-item:hover,
  .search-dropdown-item.selected {
    background: linear-gradient(135deg, #1a1d23 0%, #2c3e50 100%);
  }
  
  .search-item-title {
    color: #ffffff;
  }
  
  .search-item-type {
    color: #adb5bd;
  }
  
  .search-dropdown-footer {
    background: #343a40;
    border-top-color: #495057;
  }
  
  .search-view-all:hover {
    background: #495057;
  }
  
  .search-dropdown-empty {
    color: #adb5bd;
  }
}

/* Animation for dropdown appearance */
@keyframes searchDropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-dropdown {
  animation: searchDropdownSlide 0.2s ease-out;
}

/* Focus styles for accessibility */
.search-dropdown-item:focus {
  outline: 2px solid #00a0e9;
  outline-offset: -2px;
}

/* Loading state */
.search-input:disabled {
  background-color: #f8f9fa;
  opacity: 0.7;
}
