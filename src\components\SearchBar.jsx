import React, { useEffect, useRef } from 'react';
import { Form, InputGroup, Spinner } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useSearch } from '../contexts/SearchContext';
import { getTypeIcon, getTypeDisplayName } from '../utils/searchUtils';
import './SearchBar.css';

const SearchBar = ({ 
  placeholder = "Search services, team, technologies...", 
  showIcon = true,
  size = "md",
  className = ""
}) => {
  const navigate = useNavigate();
  const {
    searchQuery,
    suggestions,
    isSearching,
    isSearchOpen,
    selectedSuggestionIndex,
    searchInputRef,
    handleSearchChange,
    handleSearchSubmit,
    clearSearch,
    openSearch,
    closeSearch,
    handleKeyDown,
    selectSuggestion
  } = useSearch();

  const dropdownRef = useRef(null);

  // Handle click outside to close search
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target)
      ) {
        closeSearch();
      }
    };

    if (isSearchOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isSearchOpen, closeSearch]);

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    selectSuggestion(suggestion);
    navigate(suggestion.path);
    closeSearch();
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    handleSearchSubmit();
    closeSearch();
  };

  // Handle input focus
  const handleInputFocus = () => {
    openSearch();
  };

  // Handle clear button click
  const handleClearClick = () => {
    clearSearch();
    searchInputRef.current?.focus();
  };

  return (
    <div className={`search-bar-container ${className}`} style={{ position: 'relative' }}>
      <Form onSubmit={handleSubmit}>
        <InputGroup size={size}>
          {showIcon && (
            <InputGroup.Text className="search-icon">
              <i className="fas fa-search"></i>
            </InputGroup.Text>
          )}
          
          <Form.Control
            ref={searchInputRef}
            type="text"
            placeholder={placeholder}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            className="search-input"
            autoComplete="off"
            aria-label="Search"
            aria-expanded={isSearchOpen}
            aria-haspopup="listbox"
            role="combobox"
          />
          
          {/* Loading spinner */}
          {isSearching && (
            <InputGroup.Text>
              <Spinner animation="border" size="sm" />
            </InputGroup.Text>
          )}
          
          {/* Clear button */}
          {searchQuery && !isSearching && (
            <InputGroup.Text 
              className="search-clear"
              onClick={handleClearClick}
              style={{ cursor: 'pointer' }}
            >
              <i className="fas fa-times"></i>
            </InputGroup.Text>
          )}
        </InputGroup>
      </Form>

      {/* Search Results Dropdown */}
      {isSearchOpen && (
        <div 
          ref={dropdownRef}
          className="search-dropdown"
          role="listbox"
          aria-label="Search suggestions"
        >
          {suggestions.length > 0 ? (
            <>
              {!searchQuery && (
                <div className="search-dropdown-header">
                  <i className="fas fa-fire me-2"></i>
                  Popular Searches
                </div>
              )}
              
              {suggestions.map((suggestion, index) => (
                <div
                  key={suggestion.id}
                  className={`search-dropdown-item ${
                    index === selectedSuggestionIndex ? 'selected' : ''
                  }`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  role="option"
                  aria-selected={index === selectedSuggestionIndex}
                >
                  <div className="search-item-icon">
                    <i className={`fas ${getTypeIcon(suggestion.type)}`}></i>
                  </div>
                  
                  <div className="search-item-content">
                    <div className="search-item-title">
                      {suggestion.title}
                    </div>
                    <div className="search-item-type">
                      {getTypeDisplayName(suggestion.type)}
                    </div>
                  </div>
                  
                  <div className="search-item-arrow">
                    <i className="fas fa-arrow-right"></i>
                  </div>
                </div>
              ))}
              
              {searchQuery && suggestions.length > 0 && (
                <div className="search-dropdown-footer">
                  <div 
                    className="search-view-all"
                    onClick={() => {
                      handleSearchSubmit();
                      navigate('/search-results');
                      closeSearch();
                    }}
                  >
                    <i className="fas fa-search me-2"></i>
                    View all results for "{searchQuery}"
                  </div>
                </div>
              )}
            </>
          ) : searchQuery && !isSearching ? (
            <div className="search-dropdown-empty">
              <i className="fas fa-search-minus mb-2"></i>
              <div>No results found for "{searchQuery}"</div>
              <small className="text-muted">Try different keywords or check spelling</small>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
