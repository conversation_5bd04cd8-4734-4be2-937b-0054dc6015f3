import React, { useEffect, useState, useRef } from 'react';
import { Con<PERSON>er, <PERSON>, Col, Button, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import 'animate.css';

gsap.registerPlugin(ScrollTrigger);

const physicalVerificationData = {
  hero: {
    title: "Precision Physical Verification Services",
    subtitle: "Ensuring your semiconductor designs meet all manufacturing and quality standards with unmatched precision and reliability.",
  },
  intro: {
    title: "Comprehensive Physical Verification Solutions",
    description: "Our physical verification services include Design Rule Checking (DRC), Layout Versus Schematic (LVS), Electrical Rule Checking (ERC), and antenna checks to guarantee your chip's manufacturability and reliability. We ensure your design is robust and production-ready.",
    image: "https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80"
  },
  services: [
    {
      id: "drc",
      title: "Design Rule Checking (DRC)",
      description: "Verifying that the layout strictly adheres to the foundry's design rules to prevent manufacturing defects and ensure yield.",
      features: [
        "Foundry rule deck development",
        "Automated DRC runs & sign-off",
        "Efficient error analysis and reporting",
        "Hierarchical & flat DRC methodologies"
      ],
      icon: "fas fa-ruler-combined",
      image: "https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      color: "#00a0e9"
    },
    {
      id: "lvs",
      title: "Layout Versus Schematic (LVS)",
      description: "Ensuring the physical layout precisely matches the original schematic netlist to avoid functional errors and ensure design integrity.",
      features: [
        "Accurate netlist extraction",
        "Comprehensive layout-to-schematic comparison",
        "Efficient mismatch resolution and debugging",
        "Handling complex hierarchical designs"
      ],
      icon: "fas fa-project-diagram",
      image: "https://images.unsplash.com/photo-1518773553398-650c184e0bb3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      color: "#0056b3"
    },
    {
      id: "erc",
      title: "Electrical Rule Checking (ERC)",
      description: "Performing critical checks for electrical violations such as antenna effects, floating nodes, and power issues that can impact reliability.",
      features: [
        "Advanced antenna checks & fixing",
        "Robust power network verification",
        "Signal integrity analysis & optimization",
        "Electrostatic discharge (ESD) verification"
      ],
      icon: "fas fa-bolt",
      image: "https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      color: "#00a0e9"
    },
    {
      id: "dfm",
      title: "Design for Manufacturability (DFM)",
      description: "Analyzing and optimizing the layout to improve manufacturing yield and reliability by identifying process hot spots.",
      features: [
        "Critical area analysis (CAA)",
        "Lithography Hotspot Detection",
        "Yield Enhancement Analysis",
        "Advanced process variation checks"
      ],
      icon: "fas fa-industry",
      image: "https://images.unsplash.com/photo-1581092497678-83149e32f523?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      color: "#0056b3"
    }
  ],
  cta: {
    title: "Ensure Your Chip's Quality and Reliability",
    text: "Partner with us for comprehensive physical verification. Let's guarantee your chip's manufacturability and achieve flawless production.",
    buttonText: "Contact Us Today",
    buttonLink: "/contact"
  }
};

const PhysicalVerificationPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [isCtaButtonHovered, setIsCtaButtonHovered] = useState(false);
  const [animatedElements, setAnimatedElements] = useState({});
  const elementsRef = useRef({});
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const floatingElementsRef = useRef([]);
  const particlesRef = useRef([]);

  // Style constants matching HomePage
  const primaryColor = '#002956';
  const primaryLightColor = '#00a0e9';
  const primaryRgb = '0, 160, 233';
  const darkBackgroundGradient = 'linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)';

  // Button styles
  const ctaButtonBaseStyle = {
    padding: '1.2rem 3rem',
    fontSize: '1.2rem',
    background: `linear-gradient(95deg, ${primaryLightColor}, ${primaryColor})`,
    border: 'none',
    borderRadius: '50px',
    boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
    transition: 'all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)',
    transform: 'translateY(0)',
    color: '#fff',
    textDecoration: 'none',
    display: 'inline-block',
    fontWeight: 'bold',
  };

  const ctaButtonHoverStyle = {
    boxShadow: `0 15px 30px rgba(${primaryRgb}, 0.4)`,
    transform: 'translateY(-3px)'
  };

  // Set up Intersection Observer for general section animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setAnimatedElements((prev) => ({
              ...prev,
              [entry.target.id]: true
            }));
            if (entry.target.classList.contains('animate__animated')) {
              observer.unobserve(entry.target);
            }
          }
        });
      },
      { threshold: 0.1 }
    );

    Object.keys(elementsRef.current).forEach((key) => {
      if (elementsRef.current[key]) {
        observer.observe(elementsRef.current[key]);
      }
    });

    return () => {
      Object.keys(elementsRef.current).forEach((key) => {
        if (elementsRef.current[key]) {
          observer.unobserve(elementsRef.current[key]);
        }
      });
    };
  }, []);

  // GSAP Animations for Hero and ScrollTriggers for other sections
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero entrance animation
      const tl = gsap.timeline();

      tl.from(titleRef.current, {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out"
      })
      .from(subtitleRef.current, {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out"
      }, "-=0.8");

      // Floating elements animation (background)
      floatingElementsRef.current.forEach((el, index) => {
        if (el) {
          gsap.to(el, {
            y: -20,
            duration: 2 + index * 0.5,
            repeat: -1,
            yoyo: true,
            ease: "power2.inOut",
            delay: index * 0.3
          });
        }
      });

      // Particles animation (background)
      particlesRef.current.forEach((particle, index) => {
        if (particle) {
          gsap.to(particle, {
            y: -100,
            x: Math.random() * 100 - 50,
            opacity: 0,
            duration: 3 + Math.random() * 2,
            repeat: -1,
            ease: "none",
            delay: Math.random() * 3
          });
        }
      });

      // GSAP ScrollTrigger for Intro Section
      gsap.from("#intro-section .col-lg-6", {
        x: (i, target) => i % 2 === 0 ? -100 : 100,
        opacity: 0,
        duration: 1.5,
        ease: "power3.out",
        stagger: 0.3,
        scrollTrigger: {
          trigger: "#intro-section",
          start: "top 80%",
          toggleActions: "play none none none",
        }
      });

      // GSAP ScrollTrigger for Services Header
      gsap.from("#services-section #services-header", {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: "#services-section",
          start: "top 75%",
          toggleActions: "play none none none",
        }
      });

      // GSAP ScrollTrigger for Service Cards
      gsap.from(".service-card", {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        stagger: 0.15,
        scrollTrigger: {
          trigger: "#services-section .row",
          start: "top 80%",
          toggleActions: "play none none none",
        }
      });

      // GSAP ScrollTrigger for CTA Section
      gsap.from("#cta-section h2, #cta-section p, #cta-section .btn", {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        stagger: 0.2,
        scrollTrigger: {
          trigger: "#cta-section",
          start: "top 80%",
          toggleActions: "play none none none",
        }
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Helper function to set refs for Intersection Observer
  const setRef = (el, id) => {
    if (el) {
      elementsRef.current[id] = el;
    }
  };

  return (
    <div className="physical-verification-page overflow-hidden">
      {/* Stunning Hero Section - Replicated from HomePage */}
      <section
        ref={heroRef}
        className="position-relative overflow-hidden"
        style={{
          minHeight: '100vh',
          background: darkBackgroundGradient,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '100px 0'
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Geometric Shapes */}
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              ref={el => floatingElementsRef.current[i] = el}
              className="position-absolute"
              style={{
                width: `${60 + Math.random() * 40}px`,
                height: `${60 + Math.random() * 40}px`,
                background: `linear-gradient(45deg, rgba(${primaryRgb}, 0.1), rgba(${primaryRgb}, 0.3))`,
                borderRadius: Math.random() > 0.5 ? '50%' : '20%',
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                backdropFilter: 'blur(10px)',
                border: `1px solid rgba(${primaryRgb}, 0.2)`,
                boxShadow: `0 8px 32px rgba(${primaryRgb}, 0.1)`
              }}
            />
          ))}

          {/* Floating Particles */}
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              ref={el => particlesRef.current[i] = el}
              className="position-absolute"
              style={{
                width: '4px',
                height: '4px',
                background: primaryLightColor,
                borderRadius: '50%',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: 0.6,
                boxShadow: `0 0 10px ${primaryLightColor}`
              }}
            />
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: '300px',
              height: '300px',
              background: `radial-gradient(circle, rgba(${primaryRgb}, 0.15) 0%, transparent 70%)`,
              borderRadius: '50%',
              top: '20%',
              right: '10%',
              filter: 'blur(40px)'
            }}
          />
          <div
            className="position-absolute"
            style={{
              width: '200px',
              height: '200px',
              background: `radial-gradient(circle, rgba(${primaryRgb}, 0.1) 0%, transparent 70%)`,
              borderRadius: '50%',
              bottom: '30%',
              left: '15%',
              filter: 'blur(30px)'
            }}
          />
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <Row className="align-items-center justify-content-center text-center">
            <Col lg={10}>
              <div ref={titleRef} className="mb-4">
                <h1
                  className="display-1 fw-bold mb-4"
                  style={{
                    fontSize: 'clamp(3rem, 8vw, 4.4rem)',
                    lineHeight: '1.1',
                    background: `linear-gradient(135deg, #ffffff 0%, ${primaryLightColor} 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: `0 0 30px rgba(${primaryRgb}, 0.3)`
                  }}
                >
                  {physicalVerificationData.hero.title}
                </h1>
              </div>

              <div ref={subtitleRef}>
                <p
                  className="lead mb-5 mx-auto"
                  style={{
                    fontSize: '1.2rem',
                    lineHeight: '1.6',
                    color: 'rgba(255, 255, 255, 0.9)',
                    maxWidth: '800px'
                  }}
                >
                  {physicalVerificationData.hero.subtitle}
                </p>
              </div>

              <div ref={ctaRef}>
                <Link
                  to="/contact"
                  className="btn btn-lg rounded-pill fw-bold px-5 py-3"
                  style={{
                    ...ctaButtonBaseStyle,
                    ...(isCtaButtonHovered ? ctaButtonHoverStyle : {}),
                  }}
                  onMouseEnter={() => setIsCtaButtonHovered(true)}
                  onMouseLeave={() => setIsCtaButtonHovered(false)}
                >
                  Explore Services <i className="fas fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </Col>
          </Row>
        </Container>

        {/* CSS Animations */}
        <style>{`
          @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
          }
          @keyframes orbit {
            from { transform: translate(-50%, -50%) rotate(0deg) translateX(var(--radius)) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg) translateX(var(--radius)) rotate(-360deg); }
          }
          @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-15px,0); }
            70% { transform: translate3d(0,-7px,0); }
            90% { transform: translate3d(0,-2px,0); }
          }
          @keyframes dash {
            to { stroke-dashoffset: -20; }
          }
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes fadeInOut {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
          }
          @keyframes rotate3D {
            0% { transform: rotateY(0deg) rotateX(0deg); }
            25% { transform: rotateY(90deg) rotateX(15deg); }
            50% { transform: rotateY(180deg) rotateX(0deg); }
            75% { transform: rotateY(270deg) rotateX(-15deg); }
            100% { transform: rotateY(360deg) rotateX(0deg); }
          }
          @keyframes logoGlow {
            0% {
              filter: drop-shadow(0 0 20px rgba(0, 160, 233, 0.8)) brightness(1.2);
              transform: scale(1);
            }
            100% {
              filter: drop-shadow(0 0 30px rgba(0, 160, 233, 1)) brightness(1.4);
              transform: scale(1.05);
            }
          }
          @keyframes iconPulse {
            0%, 100% {
              transform: scale(1);
              opacity: 0.8;
            }
            50% {
              transform: scale(1.1);
              opacity: 1;
            }
          }
          @keyframes ringRotate0 {
            0% { transform: translate(-50%, -50%) rotateX(0deg) translateZ(0px) rotateZ(0deg); }
            100% { transform: translate(-50%, -50%) rotateX(0deg) translateZ(0px) rotateZ(360deg); }
          }
          @keyframes ringRotate1 {
            0% { transform: translate(-50%, -50%) rotateX(60deg) translateZ(15px) rotateZ(0deg); }
            100% { transform: translate(-50%, -50%) rotateX(60deg) translateZ(15px) rotateZ(-360deg); }
          }
          @keyframes ringRotate2 {
            0% { transform: translate(-50%, -50%) rotateX(120deg) translateZ(30px) rotateZ(0deg); }
            100% { transform: translate(-50%, -50%) rotateX(120deg) translateZ(30px) rotateZ(360deg); }
          }
          @keyframes particleOrbit {
            0% {
              transform: translate(-50%, -50%) rotate(0deg) translateX(80px) scale(1);
              opacity: 0.6;
            }
            50% {
              transform: translate(-50%, -50%) rotate(180deg) translateX(80px) scale(1.2);
              opacity: 1;
            }
            100% {
              transform: translate(-50%, -50%) rotate(360deg) translateX(80px) scale(1);
              opacity: 0.6;
            }
          }
          @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
          }
          @keyframes floatOrb0 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(30px, -20px) scale(1.1); }
          }
          @keyframes floatOrb1 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-25px, 15px) scale(0.9); }
          }
          @keyframes floatOrb2 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(20px, 25px) scale(1.2); }
          }
          @keyframes floatOrb3 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-30px, -10px) scale(0.8); }
          }
          @keyframes floatOrb4 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(15px, -30px) scale(1.1); }
          }
          @keyframes floatOrb5 {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-20px, 20px) scale(0.9); }
          }
          @keyframes float {
            0%, 100% {
              transform: translateY(0px) rotate(0deg);
              opacity: 0.7;
            }
            33% {
              transform: translateY(-15px) rotate(120deg);
              opacity: 1;
            }
            66% {
              transform: translateY(5px) rotate(240deg);
              opacity: 0.8;
            }
          }
        `}</style>
      </section>

      {/* Intro Section - Updated for new color scheme */}
      <section className="py-5" id="intro-section" ref={(el) => setRef(el, 'intro-section')}
        style={{ background: darkBackgroundGradient }}> {/* Applied dark background */}
        <Container>
          <Row className="align-items-center g-5">
            <Col lg={6}>
              <div className="position-relative">
                <span
                  style={{
                    display: 'inline-block',
                    padding: '8px 16px',
                    background: `rgba(${primaryRgb}, 0.2)`,
                    borderRadius: '30px',
                    color: primaryLightColor,
                    fontSize: '1rem',
                    fontWeight: 600,
                    marginBottom: '1.5rem',
                    backdropFilter: 'blur(10px)',
                    border: `1px solid rgba(${primaryRgb}, 0.3)`,
                    boxShadow: `0 5px 15px rgba(${primaryRgb}, 0.2)`
                  }}
                >
                  <i className="fas fa-check-circle me-2"></i> Physical Verification
                </span>
              </div>
              <h2 className="display-4 fw-bold mb-4" style={{ color: 'white' }}> {/* Text color changed to white */}
                {physicalVerificationData.intro.title}
              </h2>
              <p className="lead mb-4" style={{ fontSize: '1.1rem', lineHeight: '1.8', color: 'rgba(255, 255, 255, 0.9)' }}> {/* Text color changed to light white */}
                {physicalVerificationData.intro.description}
              </p>
            </Col>
            <Col lg={6}>
              <div className="position-relative rounded-4 overflow-hidden shadow-lg" style={{ height: '400px' }}>
                <img
                  src={physicalVerificationData.intro.image}
                  alt="Physical Verification"
                  className="w-100 h-100"
                  style={{ objectFit: 'cover', transition: 'transform 0.6s ease' }}
                  onMouseEnter={(e) => { e.target.style.transform = 'scale(1.05)' }}
                  onMouseLeave={(e) => { e.target.style.transform = 'scale(1)' }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Services Section - Updated for new color scheme and GSAP animations */}
      <section className="py-5" id="services-section" style={{ background: darkBackgroundGradient }}> {/* Applied dark background */}
        <Container>
          <div
            id="services-header"
            ref={(el) => setRef(el, 'services-header')}
            className="text-center mb-5"
          >
            <span
              style={{
                display: 'inline-block',
                padding: '8px 16px',
                background: `rgba(${primaryRgb}, 0.1)`,
                borderRadius: '30px',
                color: primaryLightColor,
                fontSize: '1rem',
                fontWeight: 600,
                marginBottom: '1rem',
                backdropFilter: 'blur(10px)',
                border: `1px solid rgba(${primaryRgb}, 0.2)`,
                boxShadow: `0 5px 15px rgba(${primaryRgb}, 0.1)`
              }}
            >
              <i className="fas fa-tools me-2"></i> Our Physical Verification Services
            </span>
            <h2 className="display-4 fw-bold mb-3" style={{ color: 'white' }}>Ensuring Silicon Perfection</h2> {/* Text color changed to white */}
            <p className="lead mx-auto" style={{ maxWidth: '700px', color: 'rgba(255, 255, 255, 0.9)' }}> {/* Text color changed to light white */}
              We offer a full range of physical verification services to ensure your semiconductor designs are manufacturable, reliable, and error-free.
            </p>
          </div>

          {/* Services Grid */}
          <Row className="g-4">
            {physicalVerificationData.services.map((service, index) => (
              <Col
                key={service.id}
                lg={4}
                md={6}
                sm={12}
                className="service-card-col"
              >
                <Card
                  className="h-100 shadow-sm border-0 rounded-4 service-card"
                  style={{
                    overflow: 'hidden',
                    background: 'rgba(0,0,0,0.2)', // Darker card background for contrast
                    transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out, background 0.3s ease-in-out',
                    cursor: 'pointer',
                    border: `1px solid rgba(${primaryRgb}, 0.1)` // Subtle border
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-10px)';
                    e.currentTarget.style.boxShadow = `0 15px 30px rgba(${primaryRgb}, 0.2)`;
                    e.currentTarget.style.background = `rgba(${primaryRgb}, 0.05)`; // Lighter background on hover
                    const overlay = e.currentTarget.querySelector('.service-icon-overlay');
                    if (overlay) overlay.style.opacity = 1;
                    const imgContainer = e.currentTarget.querySelector('.service-card-img-container');
                    if (imgContainer) imgContainer.style.filter = 'brightness(1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = `0 5px 15px rgba(0,0,0,0.08)`;
                    e.currentTarget.style.background = 'rgba(0,0,0,0.2)';
                    const overlay = e.currentTarget.querySelector('.service-icon-overlay');
                    if (overlay) overlay.style.opacity = 0;
                    const imgContainer = e.currentTarget.querySelector('.service-card-img-container');
                    if (imgContainer) imgContainer.style.filter = 'brightness(0.7)';
                  }}
                >
                  <div
                    className="service-card-img-container"
                    style={{
                      height: '200px',
                      overflow: 'hidden',
                      position: 'relative',
                      backgroundImage: `url(${service.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      filter: 'brightness(0.7)',
                      transition: 'filter 0.3s ease-in-out'
                    }}
                  >
                    <div
                      className="service-icon-overlay d-flex align-items-center justify-content-center"
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        backgroundColor: `${service.color}cc`,
                        opacity: 0,
                        transition: 'opacity 0.3s ease-in-out'
                      }}
                    >
                      <i className={`${service.icon} fa-4x text-white`}></i>
                    </div>
                  </div>
                  <Card.Body className="d-flex flex-column p-4">
                    <Card.Title className="fw-bold mb-3" style={{ color: primaryLightColor, fontSize: '1.6rem' }}> {/* Title changed to light blue */}
                      {service.title}
                    </Card.Title>
                    <Card.Text className="flex-grow-1" style={{ fontSize: '1.05rem', color: 'rgba(255, 255, 255, 0.8)' }}> {/* Text changed to light white */}
                      {service.description}
                    </Card.Text>
                    <ul className="list-unstyled mt-3">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="mb-2" style={{ fontSize: '0.95rem', color: 'rgba(255, 255, 255, 0.7)' }}> {/* Features changed to light white */}
                          <i className="fas fa-check-circle me-2" style={{ color: service.color }}></i> {feature}
                        </li>
                      ))}
                    </ul>
                    <Button variant="link" className="mt-3 text-decoration-none fw-bold" style={{ color: service.color }}>
                      Learn More <i className="fas fa-arrow-right ms-2"></i>
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

    </div>
  );
};

export default PhysicalVerificationPage;